import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '../../ui/card';
import { Briefcase, Building, MapPin, Pencil } from 'lucide-react';
import { formatDate, calculateDuration } from '../utils/profileUtils';

export function ExperienceTab({ experience, onEditSection }) {
	return (
		<Card>
			<CardHeader className="bg-slate-50">
				<div className="flex justify-between items-center">
					<div>
						<CardTitle className="flex items-center gap-2">
							<Briefcase className="h-5 w-5 text-indigo-600" />
							Professional Experience
						</CardTitle>
						<CardDescription>
							Your work experience. Click edit to request changes.
						</CardDescription>
					</div>
					<Button
						variant="outline"
						size="sm"
						onClick={() => onEditSection('experience')}
						className="flex items-center self-start gap-2"
					>
						<Pencil className="h-4 w-4" />
						Edit Section
					</Button>
				</div>
			</CardHeader>
			<CardContent className="pt-6">
				{experience && experience.length > 0 ? (
					<div className="space-y-6">
						{experience.map((exp) => (
							<div
								key={exp._id}
								className="relative pl-8 border-l-2 border-indigo-200 pb-6"
							>
								<div className="absolute -left-3 top-0 bg-indigo-600 rounded-full p-1">
									<Building className="h-4 w-4 text-white" />
								</div>
								<div className="mb-2">
									<Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
										{formatDate(exp.periodFrom)} -{' '}
										{formatDate(exp.periodTo)}
									</Badge>
									<Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
										{calculateDuration(exp.periodFrom, exp.periodTo)}
									</Badge>
								</div>
								<h3 className="text-xl font-bold">{exp.companyName}</h3>
								<p className="text-lg font-medium">{exp.designation}</p>
								<div className="mt-2 flex items-center">
									<MapPin className="h-4 w-4 text-rose-500 mr-1" />
									<span>{exp.location}</span>
								</div>
								{exp.reasonForLeaving && (
									<div className="mt-2">
										<span className="text-sm text-muted-foreground">
											Reason for leaving: {exp.reasonForLeaving}
										</span>
									</div>
								)}
							</div>
						))}
					</div>
				) : (
					<p className="text-muted-foreground">
						No experience information available.
					</p>
				)}
			</CardContent>
		</Card>
	);
}
