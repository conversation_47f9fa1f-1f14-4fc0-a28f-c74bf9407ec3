'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import {
	Calendar,
	MessageSquare,
	Paperclip,
	Eye,
	MoreHorizontal,
	Clock,
	AlertTriangle,
	CheckCircle2,
} from 'lucide-react';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import Image from 'next/image';

export function TaskCard({ task, onTaskClick }) {
	const [imageError, setImageError] = useState(false);

	const getPriorityColor = (priority) => {
		switch (priority) {
			case 'high':
				return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
			case 'medium':
				return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
			case 'low':
				return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
		}
	};

	const getPriorityIcon = (priority) => {
		switch (priority) {
			case 'high':
				return <AlertTriangle className="h-3 w-3" />;
			case 'medium':
				return <Clock className="h-3 w-3" />;
			case 'low':
				return <CheckCircle2 className="h-3 w-3" />;
			default:
				return null;
		}
	};

	const isOverdue = new Date(task.dueDate) < new Date();
	const isDueSoon =
		new Date(task.dueDate) <= new Date(Date.now() + 3 * 24 * 60 * 60 * 1000);

	const handleCardClick = (e) => {
		// Prevent click when clicking on dropdown or other interactive elements
		if (e.target.closest('[data-no-click]')) {
			return;
		}
		onTaskClick?.(task);
	};

	return (
		<Card
			className="group cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-[1.02] bg-card"
			onClick={handleCardClick}
		>
			{/* Cover Image */}
			{task.coverImage && !imageError && (
				<div className="relative w-full h-32 overflow-hidden rounded-t-lg">
					<Image
						src={task.coverImage}
						alt={task.title}
						fill
						className="object-cover transition-transform duration-200 group-hover:scale-105"
						onError={() => setImageError(true)}
					/>
					{/* Overlay for better text readability */}
					<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
				</div>
			)}

			<CardContent className="p-4">
				{/* Labels */}
				{task.labels && task.labels.length > 0 && (
					<div className="flex flex-wrap gap-1 mb-3">
						{task.labels.slice(0, 2).map((label, index) => (
							<Badge
								key={index}
								variant="secondary"
								className="text-xs px-2 py-0.5"
							>
								{label}
							</Badge>
						))}
						{task.labels.length > 2 && (
							<Badge variant="secondary" className="text-xs px-2 py-0.5">
								+{task.labels.length - 2}
							</Badge>
						)}
					</div>
				)}

				{/* Task Title */}
				<h4 className="font-semibold text-sm mb-2 line-clamp-2 leading-tight">
					{task.title}
				</h4>

				{/* Task Description */}
				{task.description && (
					<p className="text-xs text-muted-foreground mb-3 line-clamp-2 leading-relaxed">
						{task.description}
					</p>
				)}

				{/* Priority Badge */}
				<div className="flex items-center justify-between mb-3">
					<Badge
						variant="secondary"
						className={cn(
							'text-xs flex items-center gap-1',
							getPriorityColor(task.priority)
						)}
					>
						{getPriorityIcon(task.priority)}
						{task.priority}
					</Badge>

					{/* Card Actions */}
					<div
						className="opacity-0 group-hover:opacity-100 transition-opacity"
						data-no-click
					>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="ghost" size="sm" className="h-6 w-6 p-0">
									<MoreHorizontal className="h-3 w-3" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								<DropdownMenuItem>
									<Eye className="h-4 w-4 mr-2" />
									View Details
								</DropdownMenuItem>
								<DropdownMenuItem>Edit Task</DropdownMenuItem>
								<DropdownMenuItem>Duplicate</DropdownMenuItem>
								<DropdownMenuItem className="text-destructive">
									Delete Task
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>

				{/* Due Date */}
				{task.dueDate && (
					<div
						className={cn(
							'flex items-center gap-1 mb-3 text-xs',
							isOverdue
								? 'text-red-600 dark:text-red-400'
								: isDueSoon
									? 'text-yellow-600 dark:text-yellow-400'
									: 'text-muted-foreground'
						)}
					>
						<Calendar className="h-3 w-3" />
						<span>
							{isOverdue ? 'Overdue: ' : isDueSoon ? 'Due soon: ' : 'Due: '}
							{new Date(task.dueDate).toLocaleDateString()}
						</span>
					</div>
				)}

				{/* Bottom Row: Assignees, Comments, Attachments */}
				<div className="flex items-center justify-between">
					{/* Assignees */}
					<div className="flex -space-x-1">
						{task.assignees.slice(0, 3).map((assignee, index) => (
							<Avatar
								key={assignee.id}
								className="h-6 w-6 border-2 border-background"
							>
								<AvatarImage src={assignee.avatar} />
								<AvatarFallback className="text-xs">
									{assignee.name
										.split(' ')
										.map((n) => n[0])
										.join('')}
								</AvatarFallback>
							</Avatar>
						))}
						{task.assignees.length > 3 && (
							<div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
								<span className="text-xs font-medium">
									+{task.assignees.length - 3}
								</span>
							</div>
						)}
					</div>

					{/* Interaction Counts */}
					<div className="flex items-center gap-3">
						{/* Comments */}
						{task.commentsCount > 0 && (
							<div className="flex items-center gap-1 text-xs text-muted-foreground">
								<MessageSquare className="h-3 w-3" />
								<span>{task.commentsCount}</span>
							</div>
						)}

						{/* Attachments */}
						{task.attachmentsCount > 0 && (
							<div className="flex items-center gap-1 text-xs text-muted-foreground">
								<Paperclip className="h-3 w-3" />
								<span>{task.attachmentsCount}</span>
							</div>
						)}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
