{"name": "harphr-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.10.0", "@liquid-js/qr-code-styling": "^4.0.7", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-visually-hidden": "^1.1.0", "@react-three/drei": "^9.121.4", "@react-three/fiber": "^8.17.14", "@reduxjs/toolkit": "^2.3.0", "@tabler/icons-react": "^3.30.0", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.13.0", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "dotted-map": "^2.2.3", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.12.2", "framer-motion": "^11.12.0", "input-otp": "^1.4.2", "lodash.debounce": "^4.0.8", "lucide-react": "^0.456.0", "mini-svg-data-uri": "^1.4.4", "moment": "^2.30.1", "motion": "^12.4.1", "next": "14.2.17", "next-themes": "^0.4.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-redux": "^9.1.2", "recharts": "^2.15.1", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "sonner": "^1.7.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.173.0", "three-globe": "^2.41.12", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.17", "postcss": "^8", "prettier": "^3.5.3", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1"}}