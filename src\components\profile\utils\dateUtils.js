// Helper function to convert ISO date string to YYYY-MM-DD format for form inputs
export const convertIsoToDateInput = (isoString) => {
	if (!isoString) return '';
	try {
		const date = new Date(isoString);
		if (isNaN(date.getTime())) return '';
		
		// Format as YYYY-MM-DD
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		
		return `${year}-${month}-${day}`;
	} catch (error) {
		console.error('Error converting ISO to date input:', error);
		return '';
	}
};

// Helper function to convert YYYY-MM-DD format to ISO string for payload
export const convertDateInputToIso = (dateInput) => {
	if (!dateInput) return '';
	try {
		// Create date object from YYYY-MM-DD string
		const date = new Date(dateInput + 'T00:00:00.000Z');
		if (isNaN(date.getTime())) return '';

		// Return ISO string
		return date.toISOString();
	} catch (error) {
		console.error('Error converting date input to ISO:', error);
		return '';
	}
};
