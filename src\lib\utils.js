import axios from 'axios';
import { clsx } from 'clsx';
import { toast } from 'sonner';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs) {
	return twMerge(clsx(inputs));
}

export const userRoles = {
	SUPER_ADMIN: 1,
	COUNTRY_ADMIN: 2,
	G<PERSON><PERSON><PERSON>IED_CLIENT_ADMIN: 3,
	CLIENT_ADMIN: 4,
	BUSINESS_ADMIN: 5,
	DEPARTMENT_ADMIN: 6,
	MODULE_ADMIN: 7,
	REPORTING_MANAGER: 8,
	APPROVER: 9,
	EMPLOYEE: 10,
};

export const qualificationTypes = {
	UNDER_GRADUATE: 'UNDER_GRADUATE',
	GRADUATE: 'GRADUATE',
	NO_FORMAL_EDUCATION: 'NO_FORMAL_EDUCATION',
	POST_GRADUATE: 'POST_GRADUATE',
};

export const weeksInMonth = 4.33;

export const customFetch = axios.create({
	baseURL: 'http://localhost:5000/api/v1', // localhost
	// baseURL: 'https://tms-backend-muzr.onrender.com/api/v1', // render staging
	// baseURL: 'https://harp-hr-backend.onrender.com/api/v1', // render production

	withCredentials: true,
});

export const getInitials = (name) => {
	if (!name) return '';
	return name
		.split(' ')
		.map((n) => n[0])
		.join('')
		.toUpperCase();
};

export const showErrors = (errorResponse) => {
	if (!errorResponse && !errorResponse?.status) {
		return toast.error(
			'Network error or CORS issue. Please check your connection or try again later.'
		);
	}

	// Handle 404 Not Found
	if (errorResponse.status === 404) {
		return toast.error('Requested resource was not found (404).');
	}

	// Handle 429 Too Many Requests
	if (errorResponse.status === 429) {
		return toast.error(
			'Too many requests. Please slow down and try again shortly.'
		);
	}

	if (!errorResponse?.error?.errors) {
		if (!errorResponse?.message) {
			return toast.error('Something went wrong. Please try again later.');
		}
		return toast.error(errorResponse.message);
	}

	const { message, error } = errorResponse;
	const errors = error.errors;

	let errorMessages = [];

	// Collect general errors
	if (errors._errors) {
		errorMessages.push(...errors._errors);
	}

	// Collect field-specific errors
	Object.entries(errors).forEach(([field, errorObj]) => {
		if (field !== '_errors' && errorObj._errors) {
			errorObj._errors.forEach((msg) => {
				errorMessages.push(`${field}: ${msg}`);
			});
		}
	});

	// Display errors inside an ordered list
	toast.error(message, {
		description: errorMessages
			.map((msg, idx) => `${idx + 1}. ${msg}`)
			.join('\n'),
		duration: 7000, // Display for 7 seconds
	});
};

export const formatDate = (dateString) => {
	const date = new Date(dateString);
	return new Intl.DateTimeFormat('en-US', {
		year: 'numeric',
		month: 'short',
		day: 'numeric',
	}).format(date);
};

export const calculateAge = (dob) => {
	const today = new Date();
	const birthDate = new Date(dob);
	let age = today.getFullYear() - birthDate.getFullYear();
	const monthDiff = today.getMonth() - birthDate.getMonth();
	if (
		monthDiff < 0 ||
		(monthDiff === 0 && today.getDate() < birthDate.getDate())
	) {
		age--;
	}
	return age;
};

export const getICFinPrefixes = (watchResidentialStatus) => {
	switch (watchResidentialStatus) {
		case 'Singapore Citizen':
		case 'Singapore PR':
			return ['S', 'T'];
		case 'Employment Pass':
		case 'SPass':
		case 'Work Permit':
		case 'LOC':
			return ['F', 'G', 'M'];
		default:
			return [];
	}
};

export const formatBreadcrumb = (text) => {
	return text.replace(/-/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());
};

export const generateRandomSixCharCode = () => {
	const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
	const code = Array.from({ length: 6 }, () =>
		charset.charAt(Math.floor(Math.random() * charset.length))
	).join('');
	return code;
};
