import { useState, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '../../ui/button';
import { Input } from '../../ui/input';
import { Alert, AlertDescription, AlertTitle } from '../../ui/alert';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '../../ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '../../ui/form';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '../../ui/select';
import { Textarea } from '../../ui/textarea';
import { Baby, AlertCircle } from 'lucide-react';
import { calculateAge } from '@/lib/utils';

export function AddChildDialog({ open, onSubmit, onClose, currentFamilyData }) {
	// Helper function to convert YYYY-MM-DD format to ISO string for payload
	const convertDateInputToIso = (dateInput) => {
		if (!dateInput) return '';
		try {
			// Create date object from YYYY-MM-DD string
			const date = new Date(dateInput + 'T00:00:00.000Z');
			if (isNaN(date.getTime())) return '';

			// Return ISO string
			return date.toISOString();
		} catch (error) {
			console.error('Error converting date input to ISO:', error);
			return '';
		}
	};

	// Create a simple child validation schema
	const schema = z.object({
		name: z
			.string()
			.min(2, 'Child name must be at least 2 characters')
			.max(50, 'Child name must not exceed 50 characters')
			.regex(
				/^[a-zA-Z\s]+$/,
				'Child name must contain only letters and spaces'
			),

		dob: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format')
			.refine((date) => {
				const birthDate = new Date(date);
				const today = new Date();
				const maxAge = new Date();
				maxAge.setFullYear(today.getFullYear() - 25);

				return birthDate >= maxAge && birthDate <= today;
			}, 'Child must be born within the last 25 years and not in the future'),

		nationality: z.enum(
			[
				'singaporean',
				'malaysian',
				'indian',
				'chinese',
				'american',
				'british',
				'australian',
				'other',
			],
			{
				errorMap: () => ({ message: 'Please select a valid nationality' }),
			}
		),

		age: z
			.number()
			.min(0, 'Age must be a positive number')
			.max(100, 'Age must be below 100')
			.optional(),

		reason: z
			.string()
			.min(10, 'Reason must be at least 10 characters')
			.max(500, 'Reason must not exceed 500 characters'),
	});

	// Initialize form with react-hook-form and Zod validation
	const form = useForm({
		resolver: zodResolver(schema),
		defaultValues: {
			name: '',
			dob: '',
			nationality: '',
			age: 0,
			reason: '',
		},
	});

	const watchDob = form.watch('dob');
	const calculatedAge = useMemo(() => calculateAge(watchDob), [watchDob]);

	useEffect(() => {
		if (form.getValues('age') !== calculatedAge) {
			form.setValue('age', calculatedAge, { shouldDirty: true });
		}
	}, [calculatedAge, form]);

	// Reset form when dialog opens/closes
	useEffect(() => {
		if (!open) {
			form.reset({
				name: '',
				dob: '',
				nationality: '',
				age: 0,
				reason: '',
			});
		}
	}, [open, form]);

	// Handle form submission
	const handleFormSubmit = async (data) => {
		const { reason, ...childData } = data;

		// Convert DOB from YYYY-MM-DD to ISO format for payload
		if (childData.dob) {
			childData.dob = convertDateInputToIso(childData.dob);
		}

		// Submit the request and wait for result
		const success = await onSubmit(
			'family',
			currentFamilyData,
			{
				maritalStatus: currentFamilyData.maritalStatus,
				spouseName: currentFamilyData.spouseName,
				spouseEmploymentStatus: currentFamilyData.spouseEmploymentStatus,
				children: [...currentFamilyData.children, childData],
			},
			reason
		);

		// Only reset form if submission was successful
		if (success) {
			form.reset();
		}
	};

	if (!open) return null;

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="max-w-3xl w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
				<DialogHeader className="flex-shrink-0 pb-4 border-b">
					<DialogTitle className="text-xl font-semibold flex items-center gap-2">
						<Baby className="h-5 w-5 text-blue-500" />
						Add Child Information
					</DialogTitle>
					<DialogDescription className="text-sm text-muted-foreground">
						Request to add a new child to your family information. All additions
						require approval from your manager.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleFormSubmit)}
						className="flex-1 overflow-y-auto py-4"
					>
						<div className="space-y-6">
							<Alert className="border-amber-200 bg-amber-50">
								<AlertCircle className="h-4 w-4 text-amber-600" />
								<AlertTitle className="text-amber-800">
									Approval Required
								</AlertTitle>
								<AlertDescription className="text-amber-700">
									Child information will be reviewed by your reporting manager
									before being added to your profile.
								</AlertDescription>
							</Alert>

							<div className="space-y-6">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="name"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Child Name <span className="text-red-500">*</span>
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														placeholder="Enter child's full name"
														className="w-full"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="dob"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Date of Birth <span className="text-red-500">*</span>
												</FormLabel>
												<FormControl>
													<Input {...field} type="date" className="w-full" />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="age"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Age
												</FormLabel>
												<FormControl>
													<Input
														type="number"
														{...field}
														onChange={(e) =>
															field.onChange(parseInt(e.target.value, 10))
														}
														disabled
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="nationality"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Nationality <span className="text-red-500">*</span>
												</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Select nationality" />
														</SelectTrigger>
													</FormControl>
													<SelectContent className="z-50">
														<SelectItem value="singaporean">
															Singaporean
														</SelectItem>
														<SelectItem value="malaysian">Malaysian</SelectItem>
														<SelectItem value="indian">Indian</SelectItem>
														<SelectItem value="chinese">Chinese</SelectItem>
														<SelectItem value="american">American</SelectItem>
														<SelectItem value="british">British</SelectItem>
														<SelectItem value="australian">
															Australian
														</SelectItem>
														<SelectItem value="other">Other</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							<div className="space-y-2 pt-4 border-t">
								<FormField
									control={form.control}
									name="reason"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium">
												Reason for Adding Child{' '}
												<span className="text-red-500">*</span>
											</FormLabel>
											<FormControl>
												<Textarea
													{...field}
													placeholder="Please provide a reason for adding this child (e.g., new birth, adoption, etc.)..."
													className="min-h-[100px] w-full resize-none"
												/>
											</FormControl>
											<p className="text-xs text-muted-foreground">
												Provide a clear explanation to help expedite the
												approval process.
											</p>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>
					</form>
				</Form>

				<DialogFooter className="flex-shrink-0 pt-4 border-t">
					<Button type="button" variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button
						type="button"
						onClick={form.handleSubmit(handleFormSubmit)}
					>
						Submit Request
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
