import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import PerformanceAchievement from './performance-achievement';
import dayjs from 'dayjs';

const AchievementDemo = () => {
	const [selectedDemo, setSelectedDemo] = useState('bronze');

	// Demo data for different achievement levels
	const demoData = {
		bronze: {
			userProfile: {
				userFlags: {
					isRegistrationComplete: true,
					isQualificationDetailsComplete: false,
					isFamilyDetailsComplete: false,
					isSkillsDetailsComplete: false,
					isContactDetailsComplete: true,
					isEmploymentDetailsComplete: false,
					isEarningsDetailsComplete: false,
					isBenefitsDetailsComplete: false,
					isPersonalDetailsComplete: true,
				},
				personalDetails: {
					nameOnNRIC: '<PERSON>',
				},
			},
			attendanceLogs: [
				{
					type: 'clockIn',
					time: dayjs().subtract(1, 'day').hour(10).minute(30).toISOString(),
				},
				{
					type: 'clockOut',
					time: dayjs().subtract(1, 'day').hour(16).minute(0).toISOString(),
				},
			],
		},
		silver: {
			userProfile: {
				userFlags: {
					isRegistrationComplete: true,
					isQualificationDetailsComplete: true,
					isFamilyDetailsComplete: true,
					isSkillsDetailsComplete: true,
					isContactDetailsComplete: true,
					isEmploymentDetailsComplete: true,
					isEarningsDetailsComplete: false,
					isBenefitsDetailsComplete: false,
					isPersonalDetailsComplete: true,
				},
				personalDetails: {
					nameOnNRIC: 'Jane Smith',
				},
			},
			attendanceLogs: Array.from({ length: 15 }, (_, i) => [
				{
					type: 'clockIn',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(9)
						.minute(Math.random() * 30)
						.toISOString(),
				},
				{
					type: 'start-break',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(12)
						.minute(0)
						.toISOString(),
				},
				{
					type: 'end-break',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(13)
						.minute(0)
						.toISOString(),
				},
				{
					type: 'clockOut',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(17)
						.minute(Math.random() * 30)
						.toISOString(),
				},
			]).flat(),
		},
		gold: {
			userProfile: {
				userFlags: {
					isRegistrationComplete: true,
					isQualificationDetailsComplete: true,
					isFamilyDetailsComplete: true,
					isSkillsDetailsComplete: true,
					isContactDetailsComplete: true,
					isEmploymentDetailsComplete: true,
					isEarningsDetailsComplete: true,
					isBenefitsDetailsComplete: false,
					isPersonalDetailsComplete: true,
				},
				personalDetails: {
					nameOnNRIC: 'Alex Johnson',
				},
			},
			attendanceLogs: Array.from({ length: 20 }, (_, i) => [
				{
					type: 'clockIn',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(8)
						.minute(45 + Math.random() * 15)
						.toISOString(),
				},
				{
					type: 'start-break',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(12)
						.minute(0)
						.toISOString(),
				},
				{
					type: 'end-break',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(13)
						.minute(0)
						.toISOString(),
				},
				{
					type: 'clockOut',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(17)
						.minute(15 + Math.random() * 15)
						.toISOString(),
				},
			]).flat(),
		},
		platinum: {
			userProfile: {
				userFlags: {
					isRegistrationComplete: true,
					isQualificationDetailsComplete: true,
					isFamilyDetailsComplete: true,
					isSkillsDetailsComplete: true,
					isContactDetailsComplete: true,
					isEmploymentDetailsComplete: true,
					isEarningsDetailsComplete: true,
					isBenefitsDetailsComplete: true,
					isPersonalDetailsComplete: true,
				},
				personalDetails: {
					nameOnNRIC: 'Sarah Wilson',
				},
			},
			attendanceLogs: Array.from({ length: 22 }, (_, i) => [
				{
					type: 'clockIn',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(8)
						.minute(55 + Math.random() * 10)
						.toISOString(),
				},
				{
					type: 'start-break',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(12)
						.minute(0)
						.toISOString(),
				},
				{
					type: 'end-break',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(13)
						.minute(0)
						.toISOString(),
				},
				{
					type: 'clockOut',
					time: dayjs()
						.subtract(i + 1, 'day')
						.hour(17)
						.minute(30 + Math.random() * 10)
						.toISOString(),
				},
			]).flat(),
		},
	};

	const currentDemo = demoData[selectedDemo];

	return (
		<div className="space-y-6 p-6">
			<Card>
				<CardHeader>
					<CardTitle>Performance Achievement System Demo</CardTitle>
					<p className="text-sm text-muted-foreground">
						Click the buttons below to see different achievement levels in
						action
					</p>
				</CardHeader>
				<CardContent>
					<div className="flex gap-2 mb-4">
						{Object.keys(demoData).map((level) => (
							<Button
								key={level}
								variant={selectedDemo === level ? 'default' : 'outline'}
								size="sm"
								onClick={() => setSelectedDemo(level)}
								className="capitalize"
							>
								{level}
							</Button>
						))}
					</div>

					<div className="max-w-sm mx-auto">
						<PerformanceAchievement
							userProfile={currentDemo.userProfile}
							attendanceLogs={currentDemo.attendanceLogs}
						/>
					</div>

					<div className="mt-4 text-center">
						<Badge variant="outline" className="text-xs">
							Demo for {currentDemo.userProfile.personalDetails.nameOnNRIC}
						</Badge>
					</div>
				</CardContent>
			</Card>
		</div>
	);
};

export default AchievementDemo;
