@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
	height: 100%;
	overflow: hidden;
	pointer-events: auto !important;
}

body {
	font-family: Arial, Helvetica, sans-serif;
}

/* Hide default scrollbar */
::-webkit-scrollbar {
	display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

@layer utilities {
	.text-balance {
		text-wrap: balance;
	}

	/* 3D Flip Animation Utilities - Bottom to Top */
	.perspective-1000 {
		perspective: 1000px;
		perspective-origin: center bottom;
	}

	.transform-style-preserve-3d {
		transform-style: preserve-3d;
	}

	.backface-hidden {
		backface-visibility: hidden;
	}

	.rotate-x-0 {
		transform: rotateX(0deg);
	}

	.rotate-x-90 {
		transform: rotateX(90deg);
	}

	.rotate-x-neg-90 {
		transform: rotateX(-90deg);
	}

	.rotate-x-180 {
		transform: rotateX(180deg);
	}

	/* Calendar flip specific animations */
	.calendar-flip-up {
		transform-origin: center bottom;
		animation: flipUp 0.3s ease-in-out;
	}

	.calendar-flip-down {
		transform-origin: center bottom;
		animation: flipDown 0.3s ease-in-out;
	}

	@keyframes flipUp {
		0% {
			transform: rotateX(0deg);
		}
		50% {
			transform: rotateX(-90deg);
		}
		100% {
			transform: rotateX(0deg);
		}
	}

	@keyframes flipDown {
		0% {
			transform: rotateX(0deg);
		}
		50% {
			transform: rotateX(90deg);
		}
		100% {
			transform: rotateX(0deg);
		}
	}
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 171 44% 15%;
		--card: 0 0% 100%;
		--card-foreground: 171 44% 15%;
		--popover: 0 0% 100%;
		--popover-foreground: 171 44% 15%;
		--primary: 171 44% 24%;
		--primary-foreground: 171 44% 98%;
		--secondary: 42 97% 51%;
		--secondary-foreground: 171 44% 15%;
		--muted: 171 44% 95%;
		--muted-foreground: 171 44% 40%;
		--accent: 42 97% 51%;
		--accent-foreground: 171 44% 15%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 171 44% 90%;
		--input: 171 44% 90%;
		--ring: 171 44% 24%;
		--radius: 0.5rem;
		--chart-1: 171 30% 60%;
		--chart-2: 42 70% 65%;
		--chart-3: 197 28% 58%;
		--chart-4: 43 50% 72%;
		--chart-5: 27 60% 70%;
		--chart-6: 347 50% 62%;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 171 44% 20%;
		--sidebar-primary: 171 44% 24%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 42 97% 51%;
		--sidebar-accent-foreground: 171 44% 15%;
		--sidebar-border: 171 44% 90%;
		--sidebar-ring: 171 44% 40%;
	}

	.dark {
		--background: 171 44% 10%;
		--foreground: 0 0% 95%;
		--card: 171 44% 15%;
		--card-foreground: 0 0% 95%;
		--popover: 171 44% 8%;
		--popover-foreground: 0 0% 95%;
		--primary: 171 44% 50%;
		--primary-foreground: 171 44% 98%;
		--secondary: 42 97% 51%;
		--secondary-foreground: 171 44% 15%;
		--muted: 171 44% 20%;
		--muted-foreground: 0 0% 80%;
		--accent: 42 97% 51%;
		--accent-foreground: 171 44% 15%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 85.7% 97.3%;
		--border: 171 44% 25%;
		--input: 171 44% 25%;
		--ring: 171 44% 50%;
		--chart-1: 171 44% 50%;
		--chart-2: 42 97% 51%;
		--chart-3: 197 37% 45%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--chart-6: 347 77% 55%;
		--sidebar-background: 171 44% 12%;
		--sidebar-foreground: 0 0% 95%;
		--sidebar-primary: 171 44% 50%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 42 97% 51%;
		--sidebar-accent-foreground: 171 44% 15%;
		--sidebar-border: 171 44% 25%;
		--sidebar-ring: 171 44% 60%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}
