'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Plus,
	Calendar as CalendarIcon,
	Users,
	X,
	Image as ImageIcon,
	Tag,
	Upload,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// Form validation schema
const createTaskSchema = z.object({
	title: z.string().min(1, 'Task title is required').max(100, 'Title too long'),
	description: z.string().optional(),
	priority: z.enum(['low', 'medium', 'high'], {
		required_error: 'Please select a priority level',
	}),
	dueDate: z.date().optional(),
	coverImage: z.string().optional(),
});

// Sample team members
const sampleTeamMembers = [
	{ id: 1, name: 'John Doe', email: '<EMAIL>', avatar: null },
	{ id: 2, name: 'Jane Smith', email: '<EMAIL>', avatar: null },
	{ id: 3, name: 'Mike Johnson', email: '<EMAIL>', avatar: null },
	{ id: 4, name: 'Sarah Wilson', email: '<EMAIL>', avatar: null },
	{ id: 5, name: 'Tom Brown', email: '<EMAIL>', avatar: null },
];

// Predefined labels
const predefinedLabels = [
	'Design',
	'Development',
	'Frontend',
	'Backend',
	'UI/UX',
	'Testing',
	'Bug',
	'Feature',
	'Research',
	'Documentation',
	'Review',
	'Urgent',
];

export function CreateTaskDialog({
	children,
	columnId = 'todo',
	onTaskCreate,
}) {
	const [open, setOpen] = useState(false);
	const [selectedAssignees, setSelectedAssignees] = useState([]);
	const [selectedLabels, setSelectedLabels] = useState([]);
	const [assigneeSearchQuery, setAssigneeSearchQuery] = useState('');
	const [labelInput, setLabelInput] = useState('');
	const [coverImageUrl, setCoverImageUrl] = useState('');

	const form = useForm({
		resolver: zodResolver(createTaskSchema),
		defaultValues: {
			title: '',
			description: '',
			priority: 'medium',
			dueDate: undefined,
			coverImage: '',
		},
	});

	const onSubmit = (data) => {
		const newTask = {
			id: `task-${Date.now()}`,
			title: data.title,
			description: data.description || '',
			coverImage: coverImageUrl || null,
			priority: data.priority,
			assignees: selectedAssignees,
			dueDate: data.dueDate ? data.dueDate.toISOString().split('T')[0] : null,
			commentsCount: 0,
			attachmentsCount: 0,
			labels: selectedLabels,
			createdAt: new Date().toISOString().split('T')[0],
		};

		onTaskCreate?.(newTask, columnId);
		setOpen(false);
		form.reset();
		setSelectedAssignees([]);
		setSelectedLabels([]);
		setAssigneeSearchQuery('');
		setLabelInput('');
		setCoverImageUrl('');
	};

	const addAssignee = (member) => {
		if (!selectedAssignees.find((a) => a.id === member.id)) {
			setSelectedAssignees([...selectedAssignees, member]);
		}
		setAssigneeSearchQuery('');
	};

	const removeAssignee = (memberId) => {
		setSelectedAssignees(selectedAssignees.filter((a) => a.id !== memberId));
	};

	const addLabel = (label) => {
		if (!selectedLabels.includes(label)) {
			setSelectedLabels([...selectedLabels, label]);
		}
		setLabelInput('');
	};

	const removeLabel = (label) => {
		setSelectedLabels(selectedLabels.filter((l) => l !== label));
	};

	const filteredMembers = sampleTeamMembers
		.filter(
			(member) =>
				member.name.toLowerCase().includes(assigneeSearchQuery.toLowerCase()) ||
				member.email.toLowerCase().includes(assigneeSearchQuery.toLowerCase())
		)
		.filter((member) => !selectedAssignees.find((a) => a.id === member.id));

	const filteredLabels = predefinedLabels.filter(
		(label) =>
			label.toLowerCase().includes(labelInput.toLowerCase()) &&
			!selectedLabels.includes(label)
	);

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Create New Task</DialogTitle>
					<DialogDescription>
						Add a new task to organize your work and collaborate with your team.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						{/* Task Title */}
						<FormField
							control={form.control}
							name="title"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Task Title</FormLabel>
									<FormControl>
										<Input placeholder="Enter task title..." {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Task Description */}
						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Description</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Describe the task..."
											className="min-h-[80px]"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Cover Image */}
						<div className="space-y-3">
							<FormLabel className="flex items-center gap-2">
								<ImageIcon className="h-4 w-4" />
								Cover Image
							</FormLabel>
							<div className="flex gap-2">
								<Input
									placeholder="Enter image URL..."
									value={coverImageUrl}
									onChange={(e) => setCoverImageUrl(e.target.value)}
									className="flex-1"
								/>
								<Button type="button" variant="outline" size="sm">
									<Upload className="h-4 w-4 mr-2" />
									Upload
								</Button>
							</div>
							{coverImageUrl && (
								<div className="relative w-full h-32 rounded-lg overflow-hidden border">
									<img
										src={coverImageUrl}
										alt="Cover preview"
										className="w-full h-full object-cover"
										onError={() => setCoverImageUrl('')}
									/>
								</div>
							)}
							<FormDescription>
								Add a cover image to make your task more visual
							</FormDescription>
						</div>

						{/* Priority and Due Date Row */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* Priority */}
							<FormField
								control={form.control}
								name="priority"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Priority</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select priority" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="low">
													<div className="flex items-center gap-2">
														<div className="w-2 h-2 rounded-full bg-green-500" />
														Low
													</div>
												</SelectItem>
												<SelectItem value="medium">
													<div className="flex items-center gap-2">
														<div className="w-2 h-2 rounded-full bg-yellow-500" />
														Medium
													</div>
												</SelectItem>
												<SelectItem value="high">
													<div className="flex items-center gap-2">
														<div className="w-2 h-2 rounded-full bg-red-500" />
														High
													</div>
												</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Due Date */}
							<FormField
								control={form.control}
								name="dueDate"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Due Date (Optional)</FormLabel>
										<Popover>
											<PopoverTrigger asChild>
												<FormControl>
													<Button
														variant="outline"
														className={cn(
															'w-full pl-3 text-left font-normal',
															!field.value && 'text-muted-foreground'
														)}
													>
														{field.value ? (
															format(field.value, 'PPP')
														) : (
															<span>Pick a date</span>
														)}
														<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
													</Button>
												</FormControl>
											</PopoverTrigger>
											<PopoverContent className="w-auto p-0" align="start">
												<Calendar
													mode="single"
													selected={field.value}
													onSelect={field.onChange}
													disabled={(date) => date < new Date('1900-01-01')}
													initialFocus
												/>
											</PopoverContent>
										</Popover>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Labels */}
						<div className="space-y-3">
							<FormLabel className="flex items-center gap-2">
								<Tag className="h-4 w-4" />
								Labels
							</FormLabel>

							{/* Selected Labels */}
							{selectedLabels.length > 0 && (
								<div className="flex flex-wrap gap-2">
									{selectedLabels.map((label) => (
										<Badge
											key={label}
											variant="secondary"
											className="flex items-center gap-1 pr-1"
										>
											{label}
											<button
												type="button"
												onClick={() => removeLabel(label)}
												className="ml-1 hover:bg-muted rounded-full p-0.5"
											>
												<X className="h-3 w-3" />
											</button>
										</Badge>
									))}
								</div>
							)}

							{/* Label Input */}
							<div className="relative">
								<Input
									placeholder="Add labels..."
									value={labelInput}
									onChange={(e) => setLabelInput(e.target.value)}
									onKeyDown={(e) => {
										if (e.key === 'Enter' && labelInput.trim()) {
											e.preventDefault();
											addLabel(labelInput.trim());
										}
									}}
								/>
								{labelInput && filteredLabels.length > 0 && (
									<div className="absolute top-full left-0 right-0 z-10 mt-1 bg-popover border rounded-md shadow-md max-h-32 overflow-y-auto">
										{filteredLabels.map((label) => (
											<button
												key={label}
												type="button"
												onClick={() => addLabel(label)}
												className="w-full text-left p-2 hover:bg-muted"
											>
												{label}
											</button>
										))}
									</div>
								)}
							</div>
							<FormDescription>
								Add labels to categorize and organize your tasks
							</FormDescription>
						</div>

						{/* Assignees */}
						<div className="space-y-3">
							<FormLabel className="flex items-center gap-2">
								<Users className="h-4 w-4" />
								Assignees
							</FormLabel>

							{/* Selected Assignees */}
							{selectedAssignees.length > 0 && (
								<div className="flex flex-wrap gap-2">
									{selectedAssignees.map((assignee) => (
										<Badge
											key={assignee.id}
											variant="secondary"
											className="flex items-center gap-2 pr-1"
										>
											<Avatar className="h-4 w-4">
												<AvatarImage src={assignee.avatar} />
												<AvatarFallback className="text-xs">
													{assignee.name
														.split(' ')
														.map((n) => n[0])
														.join('')}
												</AvatarFallback>
											</Avatar>
											{assignee.name}
											<button
												type="button"
												onClick={() => removeAssignee(assignee.id)}
												className="ml-1 hover:bg-muted rounded-full p-0.5"
											>
												<X className="h-3 w-3" />
											</button>
										</Badge>
									))}
								</div>
							)}

							{/* Assignee Search */}
							<div className="relative">
								<Input
									placeholder="Search team members..."
									value={assigneeSearchQuery}
									onChange={(e) => setAssigneeSearchQuery(e.target.value)}
								/>
								{assigneeSearchQuery && filteredMembers.length > 0 && (
									<div className="absolute top-full left-0 right-0 z-10 mt-1 bg-popover border rounded-md shadow-md max-h-40 overflow-y-auto">
										{filteredMembers.map((member) => (
											<button
												key={member.id}
												type="button"
												onClick={() => addAssignee(member)}
												className="w-full flex items-center gap-3 p-3 hover:bg-muted text-left"
											>
												<Avatar className="h-6 w-6">
													<AvatarImage src={member.avatar} />
													<AvatarFallback className="text-xs">
														{member.name
															.split(' ')
															.map((n) => n[0])
															.join('')}
													</AvatarFallback>
												</Avatar>
												<div>
													<div className="font-medium">{member.name}</div>
													<div className="text-sm text-muted-foreground">
														{member.email}
													</div>
												</div>
											</button>
										))}
									</div>
								)}
							</div>
							<FormDescription>
								Assign team members to this task
							</FormDescription>
						</div>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => setOpen(false)}
							>
								Cancel
							</Button>
							<Button type="submit">
								<Plus className="h-4 w-4 mr-2" />
								Create Task
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
